package admin

import (
	"marketing/internal/dao"
	"marketing/internal/handler/admin/notice"
	"marketing/internal/pkg/db"
	"marketing/internal/service"

	"github.com/gin-gonic/gin"
)

type NoticeRouter struct{}

func NewNoticeRouter() *NoticeRouter {
	return &NoticeRouter{}
}

// Register 注册通知路由
func (e *NoticeRouter) Register(r *gin.RouterGroup) {

	//终端通知管理
	endpointNoticeRouter := r.Group("/endpoint")
	{
		endpointNoticeDao := dao.NewEndpointNoticeDao(db.DB)
		endpointNoticeService := service.NewEndpointNoticeSvc(endpointNoticeDao)
		endpointNoticeHandler := notice.NewEndpointNoticeHandler(endpointNoticeService)

		endpointNoticeRouter.GET("", endpointNoticeHandler.Lists)         // 获取通知列表
		endpointNoticeRouter.GET("/:id", endpointNoticeHandler.Get)       // 获取指定通知
		endpointNoticeRouter.POST("", endpointNoticeHandler.Create)       // 创建通知
		endpointNoticeRouter.PUT("/:id", endpointNoticeHandler.Update)    // 更新通知
		endpointNoticeRouter.DELETE("/:id", endpointNoticeHandler.Delete) // 删除通知
	}

	//推送消息类型管理
	appNotificationTypeRouter := r.Group("/type")
	{
		appNotificationTypeDao := dao.NewAppNotificationTypeDao(db.DB)
		appNotificationTypeService := service.NewAppNotificationTypeSvc(appNotificationTypeDao)
		appNotificationTypeHandler := notice.NewAppNotificationTypeHandler(appNotificationTypeService)

		appNotificationTypeRouter.GET("", appNotificationTypeHandler.Lists)               // 获取推送消息类型列表
		appNotificationTypeRouter.GET("/:id", appNotificationTypeHandler.Get)             // 获取指定推送消息类型
		appNotificationTypeRouter.GET("/detail/:slug", appNotificationTypeHandler.Detail) // 根据slug获取推送消息类型详情
		appNotificationTypeRouter.POST("", appNotificationTypeHandler.Create)             // 创建推送消息类型
		appNotificationTypeRouter.PUT("/:id", appNotificationTypeHandler.Update)          // 更新推送消息类型
		appNotificationTypeRouter.DELETE("/:id", appNotificationTypeHandler.Delete)       // 删除推送消息类型
	}
}
